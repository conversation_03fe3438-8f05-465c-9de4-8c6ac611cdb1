import React, { useState } from 'react';
import { ChefHat } from 'lucide-react';

// 用户数据
const users = [
  { id: 1, username: "admin", password: "admin123", role: "admin", name: "系统管理员", department: "信息科" },
  { id: 2, username: "nutritionist1", password: "nutri123", role: "nutritionist", name: "李营养师", department: "营养科" },
  { id: 3, username: "staff1", password: "staff123", role: "staff", name: "张员工", department: "膳食科" }
];

interface User {
  id: number;
  username: string;
  password: string;
  role: string;
  name: string;
  department: string;
}

interface LoginPageProps {
  onLogin: (user: User) => void;
}

function LoginPage({ onLogin }: LoginPageProps) {
  const [loginForm, setLoginForm] = useState({ username: '', password: '' });
  const [error, setError] = useState('');

  // 登录处理
  const handleLogin = () => {
    const user = users.find(u => 
      u.username === loginForm.username && u.password === loginForm.password
    );
    
    if (user) {
      setError('');
      onLogin(user);
    } else {
      setError('用户名或密码错误');
    }
  };

  // 处理回车键登录
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleLogin();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-xl p-8 w-full max-w-md">
        <div className="text-center mb-8">
          <div className="bg-blue-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <ChefHat className="text-white w-8 h-8" />
          </div>
          <h1 className="text-2xl font-bold text-gray-800">医院膳食系统</h1>
          <p className="text-gray-600 mt-2">请登录您的账户</p>
        </div>
        
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              用户名
            </label>
            <input
              type="text"
              value={loginForm.username}
              onChange={(e) => setLoginForm({...loginForm, username: e.target.value})}
              onKeyPress={handleKeyPress}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
              placeholder="请输入用户名"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              密码
            </label>
            <input
              type="password"
              value={loginForm.password}
              onChange={(e) => setLoginForm({...loginForm, password: e.target.value})}
              onKeyPress={handleKeyPress}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
              placeholder="请输入密码"
            />
          </div>
          
          {error && (
            <div className="text-red-600 text-sm text-center">
              {error}
            </div>
          )}
          
          <button
            onClick={handleLogin}
            className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 font-medium transition-colors"
          >
            登录
          </button>
        </div>
        
        <div className="mt-8 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-medium text-gray-800 mb-3">测试账户：</h3>
          <div className="space-y-2 text-sm text-gray-700">
            <div><strong>管理员:</strong> admin / admin123</div>
            <div><strong>营养师:</strong> nutritionist1 / nutri123</div>
            <div><strong>普通员工:</strong> staff1 / staff123</div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default LoginPage;
export { users };
export type { User };
