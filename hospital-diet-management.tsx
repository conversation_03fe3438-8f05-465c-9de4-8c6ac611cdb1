import React, { useState, useEffect } from 'react';
import {
  Menu, Search, Bell, User, Settings, LogOut, ChevronDown, ChevronRight,
  Home, Users, ShoppingCart, FileText, Database, Shield, Moon, Sun,
  Plus, Filter, Download, Edit, Trash2, Eye, MoreHorizontal, Calendar
} from 'lucide-react';
import HospitalMealManager from './meal-schedule-manager';
import HospitalMealReport from './hospital-meal-report';
import HospitalMealSystem from './hospital-meal-system';
import UserManage from './usermanage';
import LoginPage, { User as UserType } from './LoginPage';

// Mock数据
const mockDietData = [
  {
    id: 'F001',
    name: '糖尿病套餐',
    category: '治疗膳食',
    price: 28.5,
    image: '/images/diabetes-meal.jpg',
    ysyz: ['1001', '1002', '1003'],
    ysyzmc: ['普食', '流质', '半流质', '糖尿病饮食'],
    sort: 1,
    status: '启用', 
    description: '专为糖尿病患者设计的营养均衡膳食',
    calories: 1800,
    protein: 85,
    fat: 60,
    carbs: 180,
    createTime: '2024-01-15',
    updateTime: '2024-12-20'
  },
  {
    id: 'F002',
    name: '高血压营养餐',
    category: '治疗膳食',
    price: 32.0,
    image: '/images/hypertension-meal.jpg',
    ysyz: ['1001', '1004'],
    ysyzmc: ['普食', '低盐饮食'],
    sort: 2,
    status: '启用',
    description: '低盐低脂，适合高血压患者',
    calories: 1600,
    protein: 75,
    fat: 45,
    carbs: 160,
    createTime: '2024-01-20',
    updateTime: '2024-12-18'
  },
  {
    id: 'F003',
    name: '术后流质餐',
    category: '特殊膳食',
    price: 18.0,
    image: '/images/liquid-meal.jpg',
    ysyz: ['1002'],
    ysyzmc: ['流质'],
    sort: 3,
    status: '启用',
    description: '术后恢复期专用流质营养餐',
    calories: 1200,
    protein: 50,
    fat: 30,
    carbs: 120,
    createTime: '2024-02-01',
    updateTime: '2024-12-15'
  },
  {
    id: 'F004',
    name: '儿童营养餐',
    category: '普通膳食',
    price: 25.0,
    image: '/images/child-meal.jpg',
    ysyz: ['1001'],
    ysyzmc: ['普食'],
    sort: 4,
    status: '停用',
    description: '专为儿童设计的营养搭配',
    calories: 1400,
    protein: 60,
    fat: 40,
    carbs: 140,
    createTime: '2024-02-10',
    updateTime: '2024-11-30'
  },
  {
    id: 'F005',
    name: '老年半流质餐',
    category: '特殊膳食',
    price: 22.5,
    image: '/images/elderly-meal.jpg',
    ysyz: ['1003'],
    ysyzmc: ['半流质'],
    sort: 5,
    status: '启用',
    description: '适合老年人消化的半流质营养餐',
    calories: 1500,
    protein: 65,
    fat: 50,
    carbs: 150,
    createTime: '2024-02-15',
    updateTime: '2024-12-10'
  },
  {
    id: 'F006',
    name: '孕妇营养餐',
    category: '特殊膳食',
    price: 35.0,
    image: '/images/pregnant-meal.jpg',
    ysyz: ['1001', '1005'],
    ysyzmc: ['普食', '孕产妇饮食'],
    sort: 6,
    status: '启用',
    description: '孕期营养需求定制餐',
    calories: 2000,
    protein: 95,
    fat: 70,
    carbs: 200,
    createTime: '2024-03-01',
    updateTime: '2024-12-05'
  },
  {
    id: 'F007',
    name: '减重膳食',
    category: '治疗膳食',
    price: 30.0,
    image: '/images/weight-loss-meal.jpg',
    ysyz: ['1001', '1006'],
    ysyzmc: ['普食', '低热量饮食'],
    sort: 7,
    status: '启用',
    description: '科学减重营养配餐',
    calories: 1300,
    protein: 80,
    fat: 35,
    carbs: 100,
    createTime: '2024-03-10',
    updateTime: '2024-12-01'
  },
  {
    id: 'F008',
    name: '康复营养餐',
    category: '治疗膳食',
    price: 28.0,
    image: '/images/recovery-meal.jpg',
    ysyz: ['1001', '1007'],
    ysyzmc: ['普食', '高蛋白饮食'],
    sort: 8,
    status: '启用',
    description: '促进术后康复的高蛋白餐',
    calories: 1900,
    protein: 110,
    fat: 65,
    carbs: 170,
    createTime: '2024-03-15',
    updateTime: '2024-11-25'
  }
];



// 订单数据
const mockOrderData = [
  {
    id: 'O001',
    patientName: '张三',
    patientId: 'P001',
    roomNo: '101-1',
    dietItems: [
      { id: 'F001', name: '糖尿病套餐', quantity: 1, price: 28.5 }
    ],
    totalAmount: 28.5,
    orderTime: '2024-12-20 08:00:00',
    deliveryTime: '2024-12-20 12:00:00',
    status: '已完成',
    remarks: '少盐少油'
  },
  {
    id: 'O002',
    patientName: '李四',
    patientId: 'P002',
    roomNo: '102-2',
    dietItems: [
      { id: 'F002', name: '高血压营养餐', quantity: 1, price: 32.0 }
    ],
    totalAmount: 32.0,
    orderTime: '2024-12-20 08:30:00',
    deliveryTime: '2024-12-20 12:30:00',
    status: '配送中',
    remarks: ''
  },
  {
    id: 'O003',
    patientName: '王五',
    patientId: 'P003',
    roomNo: '103-1',
    dietItems: [
      { id: 'F003', name: '术后流质餐', quantity: 2, price: 18.0 }
    ],
    totalAmount: 36.0,
    orderTime: '2024-12-20 09:00:00',
    deliveryTime: '2024-12-20 13:00:00',
    status: '准备中',
    remarks: '温度适中'
  }
];

const HospitalDietManagement = () => {
  const [currentUser, setCurrentUser] = useState<UserType | null>(null);
  const [darkMode, setDarkMode] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [currentPage, setCurrentPage] = useState('仪表盘');
  const [expandedMenus, setExpandedMenus] = useState(['膳食管理']);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('全部');
  const [selectedStatus, setSelectedStatus] = useState('全部');

  const [currentPageNum, setCurrentPageNum] = useState(1);
  const [pageSize] = useState(5);
  const [selectedItems, setSelectedItems] = useState([]);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [viewMode, setViewMode] = useState('table'); // 'table' or 'card'

  // 菜单配置
  const menuItems = [
    { id: 'dashboard', name: '仪表盘', icon: Home, path: 'dashboard' },
    {
      id: 'meal-management',
      name: '膳食管理',
      icon: Calendar,
      children: [
        { id: 'meal-planning', name: '膳食计划', path: 'meal-planning' },
        { id: 'diet-dictionary', name: '膳食字典管理', path: 'diet-dictionary' }
      ]
    },
    {
      id: 'user-management',
      name: '用户管理',
      icon: Users,
      children: [
        { id: 'user-list', name: '用户列表', path: 'user-list' },
        { id: 'role-management', name: '角色管理', path: 'role-management' },
        { id: 'permission-management', name: '权限管理', path: 'permission-management' }
      ]
    },
    {
      id: 'order-management',
      name: '订单管理',
      icon: ShoppingCart,
      children: [
        { id: 'order-list', name: '订单列表', path: 'order-list' },
        { id: 'order-statistics', name: '订单统计', path: 'order-statistics' }
      ]
    },
    { id: 'reports', name: '报表中心', icon: FileText, path: 'reports' },
    {
      id: 'system-management',
      name: '系统管理',
      icon: Settings,
      children: [
        { id: 'menu-management', name: '菜单管理', path: 'menu-management' },
        { id: 'system-logs', name: '系统日志', path: 'system-logs' },
        { id: 'legacy-system', name: '旧版系统', path: 'legacy-system' }
      ]
    },
    { id: 'settings', name: '系统设置', icon: Database, path: 'settings' }
  ];

  // 登录处理
  const handleLogin = (user: UserType) => {
    setCurrentUser(user);
  };

  // 登出处理
  const handleLogout = () => {
    setCurrentUser(null);
    setCurrentPage('仪表盘');
  };

  // 切换菜单展开状态
  const toggleMenu = (menuId) => {
    setExpandedMenus(prev =>
      prev.includes(menuId)
        ? prev.filter(id => id !== menuId)
        : [...prev, menuId]
    );
  };

  // 处理菜单点击
  const handleMenuClick = (item) => {
    if (item.children) {
      toggleMenu(item.id);
    } else {
      setCurrentPage(item.name);
      // 重置相关状态
      setCurrentPageNum(1);
      setSelectedItems([]);
      setSearchTerm('');
    }
  };

  // 批量选择处理
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedItems(paginatedData.map(item => item.id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (itemId, checked) => {
    if (checked) {
      setSelectedItems([...selectedItems, itemId]);
    } else {
      setSelectedItems(selectedItems.filter(id => id !== itemId));
    }
  };

  // 查看详情
  const handleViewDetail = (item) => {
    setSelectedItem(item);
    setShowDetailModal(true);
  };

  // 导出数据
  const handleExport = () => {
    const dataToExport = filteredData.map(item => ({
      膳食编号: item.id,
      膳食名称: item.name,
      分类: item.category,
      价格: item.price,
      饮食医嘱: item.ysyzmc.join(', '),
      状态: item.status,
      热量: item.calories + 'kcal',
      蛋白质: item.protein + 'g',
      脂肪: item.fat + 'g',
      碳水化合物: item.carbs + 'g'
    }));
    
    // 这里可以添加实际的导出逻辑
    console.log('导出数据:', dataToExport);
    alert('导出功能已触发，请查看控制台');
  };

  // 过滤膳食数据
  const filteredData = mockDietData.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.category.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === '全部' || item.category === selectedCategory;
    const matchesStatus = selectedStatus === '全部' || item.status === selectedStatus;
    return matchesSearch && matchesCategory && matchesStatus;
  });

  // 分页数据
  const totalPages = Math.ceil(filteredData.length / pageSize);
  const paginatedData = filteredData.slice(
    (currentPageNum - 1) * pageSize,
    currentPageNum * pageSize
  );

  const renderSidebar = () => (
    <div className={`${sidebarCollapsed ? 'w-16' : 'w-64'} transition-all duration-300 ${
      darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
    } border-r h-full flex flex-col`}>
      {/* 侧边栏头部 */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          {!sidebarCollapsed && (
            <span className={`font-bold text-lg ${darkMode ? 'text-white' : 'text-gray-800'}`}>
              膳食管理系统
            </span>
          )}
        </div>
      </div>

      {/* 菜单列表 */}
      <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
        {menuItems.map((item) => (
          <div key={item.id}>
            <button
              onClick={() => handleMenuClick(item)}
              className={`w-full flex items-center justify-between px-3 py-2 rounded-lg transition-colors ${
                currentPage === item.name
                  ? (darkMode ? 'bg-blue-600 text-white' : 'bg-blue-50 text-blue-600')
                  : (darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100')
              }`}
            >
              <div className="flex items-center space-x-3">
                <item.icon size={20} />
                {!sidebarCollapsed && <span>{item.name}</span>}
              </div>
              {!sidebarCollapsed && item.children && (
                expandedMenus.includes(item.id) ? <ChevronDown size={16} /> : <ChevronRight size={16} />
              )}
            </button>
            
            {/* 子菜单 */}
            {!sidebarCollapsed && item.children && expandedMenus.includes(item.id) && (
              <div className="ml-8 mt-2 space-y-1">
                {item.children.map((child) => (
                  <button
                    key={child.id}
                    onClick={() => setCurrentPage(child.name)}
                    className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                      currentPage === child.name
                        ? (darkMode ? 'bg-blue-600 text-white' : 'bg-blue-50 text-blue-600')
                        : (darkMode ? 'text-gray-400 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100')
                    }`}
                  >
                    {child.name}
                  </button>
                ))}
              </div>
            )}
          </div>
        ))}
      </nav>
    </div>
  );

  const renderTopBar = () => (
    <div className={`h-16 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-b flex items-center justify-between px-6`}>
      {/* 左侧 */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          className={`p-2 rounded-lg ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
        >
          <Menu size={20} />
        </button>
        
        <div className="flex items-center space-x-2">
          <Search size={20} className="text-gray-400" />
          <input
            type="text"
            placeholder="搜索功能..."
            className={`px-3 py-1 rounded-lg border-0 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              darkMode ? 'bg-gray-700 text-white placeholder-gray-400' : 'bg-gray-100 text-gray-800 placeholder-gray-500'
            }`}
          />
        </div>
      </div>

      {/* 右侧 */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => setDarkMode(!darkMode)}
          className={`p-2 rounded-lg ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
        >
          {darkMode ? <Sun size={20} /> : <Moon size={20} />}
        </button>
        
        <button className={`p-2 rounded-lg relative ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}>
          <Bell size={20} />
          <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
        </button>
        
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
            <User size={16} className="text-white" />
          </div>
          <span className={`${darkMode ? 'text-white' : 'text-gray-800'}`}>
            {currentUser?.name || '用户'}
          </span>
          <ChevronDown size={16} />
        </div>

        <button
          onClick={handleLogout}
          className={`p-2 rounded-lg ${darkMode ? 'hover:bg-gray-700 text-red-400' : 'hover:bg-gray-100 text-red-600'}`}
          title="退出登录"
        >
          <LogOut size={20} />
        </button>
      </div>
    </div>
  );

  const renderBreadcrumb = () => (
    <div className="mb-6">
      <nav className="flex text-sm">
        <span className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>首页</span>
        <span className="mx-2">/</span>
        <span className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>系统管理</span>
        <span className="mx-2">/</span>
        <span className={`${darkMode ? 'text-white' : 'text-gray-800'}`}>{currentPage}</span>
      </nav>
    </div>
  );

  const renderDietDictionary = () => (
    <div className="space-y-6">
      {/* 操作区 */}
      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center space-x-2">
            <Search size={16} className="text-gray-400" />
            <input
              type="text"
              placeholder="搜索膳食名称或分类..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300'
              }`}
            />
          </div>
          
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className={`px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
            }`}
          >
            <option value="全部">全部分类</option>
            <option value="普通膳食">普通膳食</option>
            <option value="治疗膳食">治疗膳食</option>
            <option value="特殊膳食">特殊膳食</option>
          </select>
          
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className={`px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'
            }`}
          >
            <option value="全部">全部状态</option>
            <option value="启用">启用</option>
            <option value="停用">停用</option>
          </select>
        </div>
        
        <div className="flex items-center space-x-2">
          <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Plus size={16} />
            <span>新增膳食</span>
          </button>
          <button className={`flex items-center space-x-2 px-4 py-2 border rounded-lg transition-colors ${
            darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'
          }`}>
            <Download size={16} />
            <span>导出</span>
          </button>
        </div>
      </div>

      {/* 数据表格 */}
      <div className={`rounded-lg border overflow-hidden ${
        darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      }`}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <tr>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  膳食信息
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  分类
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  价格
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  饮食医嘱
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  状态
                </th>
                <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                  darkMode ? 'text-gray-300' : 'text-gray-500'
                }`}>
                  操作
                </th>
              </tr>
            </thead>
            <tbody className={`divide-y ${darkMode ? 'divide-gray-700' : 'divide-gray-200'}`}>
              {paginatedData.map((item) => (
                <tr key={item.id} className={`${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'}`}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-gray-300 rounded-lg flex items-center justify-center">
                        <span className="text-xs text-gray-600">图片</span>
                      </div>
                      <div className="ml-4">
                        <div className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                          {item.name}
                        </div>
                        <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          ID: {item.id}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      item.category === '治疗膳食' 
                        ? 'bg-red-100 text-red-800' 
                        : item.category === '特殊膳食'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {item.category}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      ¥{item.price}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex flex-wrap gap-1">
                      {item.ysyzmc.map((ysyz, index) => (
                        <span key={index} className={`inline-flex px-2 py-1 text-xs rounded ${
                          darkMode ? 'bg-blue-900 text-blue-200' : 'bg-blue-100 text-blue-600'
                        }`}>
                          {ysyz}
                        </span>
                      ))}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      item.status === '启用'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {item.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button className="text-blue-600 hover:text-blue-900" title="查看">
                        <Eye size={16} />
                      </button>
                      <button className="text-green-600 hover:text-green-900" title="编辑">
                        <Edit size={16} />
                      </button>
                      <button className="text-red-600 hover:text-red-900" title="删除">
                        <Trash2 size={16} />
                      </button>
                      <button className={`${darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-400 hover:text-gray-600'}`} title="更多">
                        <MoreHorizontal size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* 分页 */}
        <div className={`px-6 py-3 border-t flex items-center justify-between ${
          darkMode ? 'border-gray-700 bg-gray-750' : 'border-gray-200 bg-gray-50'
        }`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            显示 {(currentPageNum - 1) * pageSize + 1} 到 {Math.min(currentPageNum * pageSize, filteredData.length)} 项，共 {filteredData.length} 项
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPageNum(Math.max(1, currentPageNum - 1))}
              disabled={currentPageNum === 1}
              className={`px-3 py-1 border rounded-lg transition-colors ${
                currentPageNum === 1 
                  ? (darkMode ? 'border-gray-600 text-gray-500' : 'border-gray-300 text-gray-400')
                  : (darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50')
              }`}
            >
              上一页
            </button>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPageNum(page)}
                className={`px-3 py-1 border rounded-lg transition-colors ${
                  page === currentPageNum
                    ? 'bg-blue-600 border-blue-600 text-white'
                    : (darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50')
                }`}
              >
                {page}
              </button>
            ))}
            <button
              onClick={() => setCurrentPageNum(Math.min(totalPages, currentPageNum + 1))}
              disabled={currentPageNum === totalPages}
              className={`px-3 py-1 border rounded-lg transition-colors ${
                currentPageNum === totalPages 
                  ? (darkMode ? 'border-gray-600 text-gray-500' : 'border-gray-300 text-gray-400')
                  : (darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50')
              }`}
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>
  );



  const renderMainContent = () => {
    switch (currentPage) {
      case '膳食字典管理':
        return renderDietDictionary();
      case '用户列表':
        return <UserManage darkMode={darkMode} />;
      case '膳食计划':
        return <HospitalMealManager />;
      case '报表中心':
        return <HospitalMealReport />;
      case '旧版系统':
        return <HospitalMealSystem />;
      case '仪表盘':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-blue-100">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>总用户数</p>
                    <p className={`text-2xl font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>1,234</p>
                  </div>
                </div>
              </div>
              <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-green-100">
                    <ShoppingCart className="w-6 h-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>今日订单</p>
                    <p className={`text-2xl font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>89</p>
                  </div>
                </div>
              </div>
              <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-yellow-100">
                    <FileText className="w-6 h-6 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>膳食种类</p>
                    <p className={`text-2xl font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>156</p>
                  </div>
                </div>
              </div>
              <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-purple-100">
                    <Database className="w-6 h-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>系统状态</p>
                    <p className={`text-2xl font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>正常</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return (
          <div className={`text-center py-20 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            <h3 className="text-xl font-medium mb-2">{currentPage}</h3>
            <p>该功能模块正在开发中...</p>
          </div>
        );
    }
  };

  // 如果用户未登录，显示登录页面
  if (!currentUser) {
    return <LoginPage onLogin={handleLogin} />;
  }

  return (
    <div className={`min-h-screen transition-colors ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="flex h-screen">
        {/* 侧边栏 */}
        {renderSidebar()}

        {/* 主要内容区域 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* 顶部导航栏 */}
          {renderTopBar()}

          {/* 主内容区 */}
          <main className="flex-1 overflow-auto">
            <div className="max-w-7xl mx-auto px-6 py-8">
              {/* 面包屑导航 */}
              {renderBreadcrumb()}

              {/* 页面标题 */}
              <div className="mb-6">
                <h1 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {currentPage}
                </h1>
                <p className={`text-sm mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  管理医院膳食信息，包括分类、价格、饮食医嘱等
                </p>
              </div>

              {/* 动态内容 */}
              {renderMainContent()}
            </div>
          </main>

          {/* 底部版权信息 */}
          <footer className={`py-4 px-6 border-t text-center text-sm ${
            darkMode ? 'bg-gray-800 border-gray-700 text-gray-400' : 'bg-white border-gray-200 text-gray-500'
          }`}>
            © 2025 医院膳食管理系统 - 专业的营养膳食管理解决方案
          </footer>
        </div>
      </div>
    </div>
  );
};

export default HospitalDietManagement;