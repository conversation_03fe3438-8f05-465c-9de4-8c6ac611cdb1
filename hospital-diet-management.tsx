import React, { useState } from 'react';
import {
  Menu, Search, Bell, User, Settings, LogOut, ChevronDown, ChevronRight,
  Home, Users, ShoppingCart, FileText, Database, Moon, Sun, Calendar
} from 'lucide-react';
import HospitalMealManager from './meal-schedule-manager';
import HospitalMealReport from './hospital-meal-report';
import UserManage from './usermanage';
import DictManagement from './dict-management';
import LoginPage, { User as UserType } from './LoginPage';

// 订单数据
const mockOrderData = [
  {
    id: 'O001',
    patientName: '张三',
    patientId: 'P001',
    roomNo: '101-1',
    dietItems: [
      { id: 'F001', name: '糖尿病套餐', quantity: 1, price: 28.5 }
    ],
    totalAmount: 28.5,
    orderTime: '2024-12-20 08:00:00',
    deliveryTime: '2024-12-20 12:00:00',
    status: '已完成',
    remarks: '少盐少油'
  },
  {
    id: 'O002',
    patientName: '李四',
    patientId: 'P002',
    roomNo: '102-2',
    dietItems: [
      { id: 'F002', name: '高血压营养餐', quantity: 1, price: 32.0 }
    ],
    totalAmount: 32.0,
    orderTime: '2024-12-20 08:30:00',
    deliveryTime: '2024-12-20 12:30:00',
    status: '配送中',
    remarks: ''
  },
  {
    id: 'O003',
    patientName: '王五',
    patientId: 'P003',
    roomNo: '103-1',
    dietItems: [
      { id: 'F003', name: '术后流质餐', quantity: 2, price: 18.0 }
    ],
    totalAmount: 36.0,
    orderTime: '2024-12-20 09:00:00',
    deliveryTime: '2024-12-20 13:00:00',
    status: '准备中',
    remarks: '温度适中'
  }
];

const HospitalDietManagement = () => {
  const [currentUser, setCurrentUser] = useState<UserType | null>(null);
  const [darkMode, setDarkMode] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [currentPage, setCurrentPage] = useState('仪表盘');
  const [expandedMenus, setExpandedMenus] = useState(['膳食管理']);



  // 角色名称映射
  const roleNames = {
    admin: "系统管理员",
    nutritionist: "营养师",
    staff: "普通员工"
  };

  // 基于角色的菜单配置
  const menuConfig = {
    "admin": [
      { id: 'dashboard', name: '仪表盘', icon: Home, path: 'dashboard' },
      {
        id: 'meal-management',
        name: '膳食管理',
        icon: Calendar,
        children: [
          { id: 'meal-planning', name: '膳食计划', path: 'meal-planning' },
          { id: 'diet-dictionary', name: '膳食字典管理', path: 'diet-dictionary' }
        ]
      },
      {
        id: 'user-management',
        name: '用户管理',
        icon: Users,
        children: [
          { id: 'user-list', name: '用户列表', path: 'user-list' },
          { id: 'role-management', name: '角色管理', path: 'role-management' },
          { id: 'permission-management', name: '权限管理', path: 'permission-management' }
        ]
      },
      {
        id: 'order-management',
        name: '订单管理',
        icon: ShoppingCart,
        children: [
          { id: 'order-list', name: '订单列表', path: 'order-list' },
          { id: 'order-statistics', name: '订单统计', path: 'order-statistics' }
        ]
      },
      { id: 'reports', name: '报表中心', icon: FileText, path: 'reports' },
      {
        id: 'system-management',
        name: '系统管理',
        icon: Settings,
        children: [
          { id: 'menu-management', name: '菜单管理', path: 'menu-management' },
          { id: 'system-logs', name: '系统日志', path: 'system-logs' }
        ]
      },
      { id: 'settings', name: '系统设置', icon: Database, path: 'settings' }
    ],
    "nutritionist": [
      { id: 'dashboard', name: '工作台', icon: Home, path: 'dashboard' },
      {
        id: 'meal-management',
        name: '膳食管理',
        icon: Calendar,
        children: [
          { id: 'meal-planning', name: '膳食计划', path: 'meal-planning' },
          { id: 'diet-dictionary', name: '膳食字典管理', path: 'diet-dictionary' }
        ]
      },
      {
        id: 'order-management',
        name: '订单管理',
        icon: ShoppingCart,
        children: [
          { id: 'order-list', name: '订单列表', path: 'order-list' },
          { id: 'order-statistics', name: '订单统计', path: 'order-statistics' }
        ]
      },
      { id: 'reports', name: '报表中心', icon: FileText, path: 'reports' }
    ],
    "staff": [
      { id: 'dashboard', name: '工作台', icon: Home, path: 'dashboard' },
      {
        id: 'order-management',
        name: '订单管理',
        icon: ShoppingCart,
        children: [
          { id: 'order-list', name: '订单列表', path: 'order-list' }
        ]
      },
      {
        id: 'meal-management',
        name: '膳食管理',
        icon: Calendar,
        children: [
          { id: 'diet-dictionary', name: '膳食字典管理', path: 'diet-dictionary' }
        ]
      }
    ]
  };

  // 获取当前用户菜单
  const getCurrentUserMenu = () => {
    if (!currentUser) return [];
    return menuConfig[currentUser.role as keyof typeof menuConfig] || [];
  };



  // 登录处理
  const handleLogin = (user: UserType) => {
    setCurrentUser(user);
    // 根据角色设置默认页面
    const defaultPage = user.role === 'admin' ? '仪表盘' : '工作台';
    setCurrentPage(defaultPage);
  };

  // 登出处理
  const handleLogout = () => {
    setCurrentUser(null);
    setCurrentPage('仪表盘');
  };

  // 切换菜单展开状态
  const toggleMenu = (menuId: string) => {
    setExpandedMenus(prev =>
      prev.includes(menuId)
        ? prev.filter(id => id !== menuId)
        : [...prev, menuId]
    );
  };

  // 处理菜单点击
  const handleMenuClick = (item: any) => {
    if (item.children) {
      toggleMenu(item.id);
    } else {
      setCurrentPage(item.name);
    }
  };

  const renderSidebar = () => (
    <div className={`${sidebarCollapsed ? 'w-16' : 'w-64'} transition-all duration-300 ${
      darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
    } border-r h-full flex flex-col`}>
      {/* 侧边栏头部 */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          {!sidebarCollapsed && (
            <span className={`font-bold text-lg ${darkMode ? 'text-white' : 'text-gray-800'}`}>
              膳食管理系统
            </span>
          )}
        </div>
      </div>

      {/* 菜单列表 */}
      <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
        {getCurrentUserMenu().map((item) => (
          <div key={item.id}>
            <button
              onClick={() => handleMenuClick(item)}
              className={`w-full flex items-center justify-between px-3 py-2 rounded-lg transition-colors ${
                currentPage === item.name
                  ? (darkMode ? 'bg-blue-600 text-white' : 'bg-blue-50 text-blue-600')
                  : (darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100')
              }`}
            >
              <div className="flex items-center space-x-3">
                <item.icon size={20} />
                {!sidebarCollapsed && <span>{item.name}</span>}
              </div>
              {!sidebarCollapsed && item.children && (
                expandedMenus.includes(item.id) ? <ChevronDown size={16} /> : <ChevronRight size={16} />
              )}
            </button>
            
            {/* 子菜单 */}
            {!sidebarCollapsed && item.children && expandedMenus.includes(item.id) && (
              <div className="ml-8 mt-2 space-y-1">
                {item.children.map((child) => (
                  <button
                    key={child.id}
                    onClick={() => setCurrentPage(child.name)}
                    className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                      currentPage === child.name
                        ? (darkMode ? 'bg-blue-600 text-white' : 'bg-blue-50 text-blue-600')
                        : (darkMode ? 'text-gray-400 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100')
                    }`}
                  >
                    {child.name}
                  </button>
                ))}
              </div>
            )}
          </div>
        ))}
      </nav>
    </div>
  );

  const renderTopBar = () => (
    <div className={`h-16 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-b flex items-center justify-between px-6`}>
      {/* 左侧 */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          className={`p-2 rounded-lg ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
        >
          <Menu size={20} />
        </button>
        
        <div className="flex items-center space-x-2">
          <Search size={20} className="text-gray-400" />
          <input
            type="text"
            placeholder="搜索功能..."
            className={`px-3 py-1 rounded-lg border-0 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              darkMode ? 'bg-gray-700 text-white placeholder-gray-400' : 'bg-gray-100 text-gray-800 placeholder-gray-500'
            }`}
          />
        </div>
      </div>

      {/* 右侧 */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => setDarkMode(!darkMode)}
          className={`p-2 rounded-lg ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
        >
          {darkMode ? <Sun size={20} /> : <Moon size={20} />}
        </button>
        
        <button className={`p-2 rounded-lg relative ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}>
          <Bell size={20} />
          <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
        </button>
        
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
            <User size={16} className="text-white" />
          </div>
          <div className="text-right">
            <div className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-800'}`}>
              {currentUser?.name || '用户'}
            </div>
            <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              {currentUser ? roleNames[currentUser.role as keyof typeof roleNames] : ''}
            </div>
          </div>
          <ChevronDown size={16} />
        </div>

        <button
          onClick={handleLogout}
          className={`p-2 rounded-lg ${darkMode ? 'hover:bg-gray-700 text-red-400' : 'hover:bg-gray-100 text-red-600'}`}
          title="退出登录"
        >
          <LogOut size={20} />
        </button>
      </div>
    </div>
  );

  const renderBreadcrumb = () => (
    <div className="mb-6">
      <nav className="flex text-sm">
        <span className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>首页</span>
        <span className="mx-2">/</span>
        <span className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>系统管理</span>
        <span className="mx-2">/</span>
        <span className={`${darkMode ? 'text-white' : 'text-gray-800'}`}>{currentPage}</span>
      </nav>
    </div>
  );





  const renderMainContent = () => {
    switch (currentPage) {
      case '膳食字典管理':
        return <DictManagement darkMode={darkMode} />;
      case '用户列表':
        return <UserManage darkMode={darkMode} />;
      case '膳食计划':
        return <HospitalMealManager />;
      case '报表中心':
        return <HospitalMealReport />;
      case '仪表盘':
      case '工作台':
        return (
          <div className="space-y-6">
            <div className="mb-6">
              <h2 className={`text-lg font-semibold mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                欢迎使用医院膳食管理系统
              </h2>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                当前角色: {currentUser ? roleNames[currentUser.role as keyof typeof roleNames] : ''}
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-blue-100">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>总用户数</p>
                    <p className={`text-2xl font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>1,234</p>
                  </div>
                </div>
              </div>
              <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-green-100">
                    <ShoppingCart className="w-6 h-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>今日订单</p>
                    <p className={`text-2xl font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>89</p>
                  </div>
                </div>
              </div>
              <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-yellow-100">
                    <FileText className="w-6 h-6 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>膳食种类</p>
                    <p className={`text-2xl font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>156</p>
                  </div>
                </div>
              </div>
              <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-purple-100">
                    <Database className="w-6 h-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>系统状态</p>
                    <p className={`text-2xl font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>正常</p>
                  </div>
                </div>
              </div>
            </div>

            {/* 根据角色显示不同的快捷操作 */}
            {currentUser && (
              <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
                <h3 className={`text-lg font-medium mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  快捷操作
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {currentUser.role === 'admin' && (
                    <>
                      <button className="p-4 text-center rounded-lg border border-blue-200 hover:bg-blue-50 transition-colors">
                        <Users className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                        <span className="text-sm text-blue-600">用户管理</span>
                      </button>
                      <button className="p-4 text-center rounded-lg border border-green-200 hover:bg-green-50 transition-colors">
                        <Settings className="w-8 h-8 text-green-600 mx-auto mb-2" />
                        <span className="text-sm text-green-600">系统设置</span>
                      </button>
                    </>
                  )}
                  {(currentUser.role === 'admin' || currentUser.role === 'nutritionist') && (
                    <>
                      <button className="p-4 text-center rounded-lg border border-orange-200 hover:bg-orange-50 transition-colors">
                        <Calendar className="w-8 h-8 text-orange-600 mx-auto mb-2" />
                        <span className="text-sm text-orange-600">膳食计划</span>
                      </button>
                      <button className="p-4 text-center rounded-lg border border-purple-200 hover:bg-purple-50 transition-colors">
                        <FileText className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                        <span className="text-sm text-purple-600">报表中心</span>
                      </button>
                    </>
                  )}
                  <button className="p-4 text-center rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                    <ShoppingCart className="w-8 h-8 text-gray-600 mx-auto mb-2" />
                    <span className="text-sm text-gray-600">订单管理</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        );
      default:
        return (
          <div className={`text-center py-20 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            <h3 className="text-xl font-medium mb-2">{currentPage}</h3>
            <p>该功能模块正在开发中...</p>
          </div>
        );
    }
  };

  // 如果用户未登录，显示登录页面
  if (!currentUser) {
    return <LoginPage onLogin={handleLogin} />;
  }

  return (
    <div className={`min-h-screen transition-colors ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="flex h-screen">
        {/* 侧边栏 */}
        {renderSidebar()}

        {/* 主要内容区域 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* 顶部导航栏 */}
          {renderTopBar()}

          {/* 主内容区 */}
          <main className="flex-1 overflow-auto">
            <div className="max-w-7xl mx-auto px-6 py-8">
              {/* 面包屑导航 */}
              {renderBreadcrumb()}

              {/* 页面标题 */}
              <div className="mb-6">
                <h1 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {currentPage}
                </h1>
                <p className={`text-sm mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  管理医院膳食信息，包括分类、价格、饮食医嘱等
                </p>
              </div>

              {/* 动态内容 */}
              {renderMainContent()}
            </div>
          </main>

          {/* 底部版权信息 */}
          <footer className={`py-4 px-6 border-t text-center text-sm ${
            darkMode ? 'bg-gray-800 border-gray-700 text-gray-400' : 'bg-white border-gray-200 text-gray-500'
          }`}>
            © 2025 医院膳食管理系统 - 专业的营养膳食管理解决方案
          </footer>
        </div>
      </div>
    </div>
  );
};

export default HospitalDietManagement;